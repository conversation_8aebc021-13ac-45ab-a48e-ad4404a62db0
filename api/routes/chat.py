from logging import getLogger
from typing import AsyncGenerator, Optional
import time
import json
import re
import asyncio
from uuid import U<PERSON><PERSON>

from fastapi import API<PERSON>outer, HTTPException, status, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from sqlalchemy.orm import Session

from db.session import get_db

from agents.document_agent import get_document_agent
from agents.document_analysis_agent import get_document_analysis_agent
from teams.construction_team import get_construction_team
# Agent optimizer removed - functionality integrated into main flow

# ThoughtChain integration - RESTORED for agent reasoning visualization
from tools.thought_chain import (
    reasoning_context, 
    get_reasoning_tracker, 
    StreamingThoughtUpdate,
    ThoughtChainResponse,
    AgentThoughtChain
)

logger = getLogger(__name__)

######################################################
## Response Filtering Utilities - Layer 3 Safety Net
######################################################

def filter_content_text_from_response(text: str) -> str:
    """
    Remove ALL content_text fields from agent responses to prevent frontend overload.
    
    This is Layer 3 of our content filtering strategy - an aggressive safety net that removes
    ANY content_text fields that may have leaked through from vector search results.
    """
    try:
        # Aggressive patterns to match ANY content_text field (not just large ones)
        # Matches: "content_text": "any content here", or 'content_text': 'content'
        pattern_double = r'"content_text"\s*:\s*"[^"]*"'
        pattern_single = r"'content_text'\s*:\s*'[^']*'"
        
        # Replace with lightweight placeholder
        filtered_text = re.sub(pattern_double, '"content_text": "[Filtered for performance]"', text)
        filtered_text = re.sub(pattern_single, "'content_text': '[Filtered for performance]'", filtered_text)
        
        # Count how many were filtered
        original_matches = len(re.findall(pattern_double, text)) + len(re.findall(pattern_single, text))
        if original_matches > 0:
            logger.info(f"🧹 AGGRESSIVE FILTER: Removed {original_matches} content_text fields from response")
            
        return filtered_text
        
    except Exception as e:
        logger.warning(f"⚠️ Error filtering content_text from response: {e}")
        return text  # Return original if filtering fails

######################################################
## Simplified Chat Interface - Pure Agno Native
######################################################

chat_router = APIRouter(prefix="/chat", tags=["Chat"])


class AgnoStreamRequest(BaseModel):
    """Request schema for Agno streaming with ThoughtChain support."""
    message: str
    user_id: str
    agent_id: str = "construction_team"
    session_id: Optional[str] = None
    project_id: Optional[str] = None
    auth_token: Optional[str] = None
    
    # ThoughtChain options - enabled by default for structured responses
    enable_thought_chain: bool = True


def get_agent_by_id(
    agent_id: str,
    model_id: str = "gpt-4o-mini",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = False,
    project_id: Optional[str] = None,
    auth_token: Optional[str] = None,
):
    """Get agent instance based on agent_id"""
    if agent_id == "document_agent":
        return get_document_agent(
            model_id=model_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            project_id=project_id,
            auth_token=auth_token,
        )
    elif agent_id == "document_analysis_agent":
        return get_document_analysis_agent(
            model_id=model_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            project_id=project_id,
            auth_token=auth_token,
        )
    elif agent_id == "construction_team":
        return get_construction_team(
            model_id=model_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            project_id=project_id,
            auth_token=auth_token,
        )
    else:
        # Default to construction team  
        return get_construction_team(
            model_id=model_id,
            user_id=user_id,
            session_id=session_id,
            debug_mode=debug_mode,
            project_id=project_id,
            auth_token=auth_token,
        )


async def chat_response_streamer_with_db_save(agent, message: str, session_id: Optional[str] = None, 
                                            agent_id: str = None, project_id: Optional[str] = None,
                                            structured: bool = False, user_id: Optional[str] = None,
                                            enable_thought_chain: bool = True) -> AsyncGenerator:
    """
    Stream agent responses with ThoughtChain or basic structured events.

    Args:
        agent: The agent instance to interact with
        message: User message to process
        session_id: Optional session ID for context
        agent_id: Agent identifier for optimization
        project_id: Project ID for context
        structured: If True, return structured JSON events
        user_id: User ID for database saving
        enable_thought_chain: If True, use full ThoughtChain reasoning visualization

    Yields:
        Text chunks or structured events from the agent response
    """
    start_time = time.time()
    
    if enable_thought_chain and structured:
        # Use full ThoughtChain system
        async for item in _stream_with_thought_chain(
            agent, message, session_id, agent_id, user_id, project_id, start_time
        ):
            yield item
    elif structured:
        # Use basic structured events (fallback)
        async for item in _stream_with_basic_events(
            agent, message, session_id, agent_id, user_id, start_time
        ):
            yield item
    else:
        # Plain text streaming
        async for item in _stream_plain_text(
            agent, message, session_id, user_id, start_time
        ):
            yield item


async def _stream_with_thought_chain(agent, message: str, session_id: str, agent_id: str, 
                                   user_id: str, project_id: Optional[str], start_time: float) -> AsyncGenerator:
    """Stream with full ThoughtChain reasoning visualization and smart content buffering."""
    
    # Set up ThoughtChain streaming callback
    thought_chain_updates = []
    
    def thought_chain_callback(chain_id: str, update: StreamingThoughtUpdate):
        """Capture ThoughtChain updates for streaming."""
        try:
            # Stream the update immediately
            update_json = update.to_json_dict()
            sse_data = f"data: {json.dumps(update_json)}\n\n"
            thought_chain_updates.append(sse_data)
        except Exception as e:
            logger.warning(f"Error in thought chain callback: {e}")
    
    # Add callback to tracker
    tracker = get_reasoning_tracker()
    tracker.add_callback(thought_chain_callback)
    
    full_response = ""
    final_thought_chain = None
    
    # Smart content buffering
    content_buffer = ""
    buffer_timeout = 0.1  # 100ms timeout
    max_buffer_size = 15  # Max characters before forcing send
    last_buffer_time = time.time()
    
    async def send_buffered_content():
        """Send buffered content as a structured event."""
        nonlocal content_buffer
        if content_buffer:
            content_update = {
                "type": "content", 
                "data": {"text": content_buffer}
            }
            yield f"data: {json.dumps(content_update)}\n\n"
            content_buffer = ""
    
    def should_send_buffer(buffer: str) -> bool:
        """Determine if buffer should be sent based on multiple triggers."""
        current_time = time.time()
        
        # Word boundary triggers (space, punctuation, newline)
        word_boundaries = {' ', '\n', '.', ',', ';', ':', '!', '?', '-', '(', ')', '[', ']', '{', '}'}
        has_word_boundary = buffer and buffer[-1] in word_boundaries
        
        # Size trigger
        size_trigger = len(buffer) >= max_buffer_size
        
        # Time trigger 
        time_trigger = (current_time - last_buffer_time) >= buffer_timeout
        
        return has_word_boundary or size_trigger or time_trigger
    
    try:
        # Use ThoughtChain context manager
        async with reasoning_context(
            session_id=session_id,
            agent_id=agent_id,
            user_id=user_id,
            user_message=message
        ) as chain_id:
            
            # Send any queued thought chain updates
            for update_sse in thought_chain_updates:
                yield update_sse
            thought_chain_updates.clear()
            
            # Run agent with reasoning tracking
            run_response = await agent.arun(message, stream=True, session_id=session_id)
            
            async for chunk in run_response:
                # Collect full response
                full_response += chunk.content
                
                # Send any new thought chain updates
                for update_sse in thought_chain_updates:
                    yield update_sse
                thought_chain_updates.clear()
                
                # Smart content buffering
                if chunk.content:
                    filtered_content = filter_content_text_from_response(chunk.content)
                    if filtered_content:
                        # Add to buffer
                        content_buffer += filtered_content
                        last_buffer_time = time.time()
                        
                        # Check if we should send the buffer
                        if should_send_buffer(content_buffer):
                            async for buffered_event in send_buffered_content():
                                yield buffered_event
            
            # Flush any remaining buffered content
            if content_buffer:
                async for buffered_event in send_buffered_content():
                    yield buffered_event
            
            # Complete the reasoning session
            final_thought_chain = tracker.complete_reasoning_session(
                chain_id=chain_id,
                final_response=full_response,
                success=True
            )
            
            # Send any final thought chain updates
            for update_sse in thought_chain_updates:
                yield update_sse
    
    except Exception as e:
        logger.error(f"Error in ThoughtChain streaming: {e}")
        # Send error event
        error_update = {
            "type": "error",
            "data": {"message": str(e)}
        }
        yield f"data: {json.dumps(error_update)}\n\n"
    
    # Send completion event
    response_time = time.time() - start_time
    completion_data = {
        "type": "done",
        "data": {
            "duration": response_time,
            "thought_chain": final_thought_chain.to_json_dict() if final_thought_chain else None
        }
    }
    yield f"data: {json.dumps(completion_data)}\n\n"
    
    # Save to database
    await _save_to_database(full_response, session_id, user_id, agent_id, response_time)


async def _stream_with_basic_events(agent, message: str, session_id: str, agent_id: str, 
                                  user_id: str, start_time: float) -> AsyncGenerator:
    """Stream with basic structured events (original system)."""
    
    # Import here to avoid circular dependency
    from api.models.stream_events import parse_chunk_to_events, StreamEvent, EventType
    
    # Send initial thinking event
    thinking_event = StreamEvent(
        type=EventType.THINKING,
        data={"message": "Processing your request..."}
    )
    yield thinking_event.to_sse()
    
    # Run agent
    run_response = await agent.arun(message, stream=True, session_id=session_id)
    
    chunks_yielded = 0
    full_response = ""
    
    async for chunk in run_response:
        chunks_yielded += 1
        full_response += chunk.content
        
        # Parse chunk into structured events
        events = parse_chunk_to_events(chunk.content)
        for event in events:
            yield event.to_sse()
    
    # Send done event
    done_event = StreamEvent(
        type=EventType.DONE,
        data={"chunks": chunks_yielded, "duration": time.time() - start_time}
    )
    yield done_event.to_sse()
    
    # Save to database
    response_time = time.time() - start_time
    await _save_to_database(full_response, session_id, user_id, agent_id, response_time)


async def _stream_plain_text(agent, message: str, session_id: str, user_id: str, start_time: float) -> AsyncGenerator:
    """Stream plain text without structured events."""
    
    run_response = await agent.arun(message, stream=True, session_id=session_id)
    full_response = ""
    
    async for chunk in run_response:
        full_response += chunk.content
        # Layer 3 Safety Net: Filter content_text from streaming chunks
        filtered_content = filter_content_text_from_response(chunk.content)
        yield filtered_content
    
    # Save to database
    response_time = time.time() - start_time
    await _save_to_database(full_response, session_id, user_id, None, response_time)


async def _save_to_database(full_response: str, session_id: str, user_id: str, 
                          agent_id: Optional[str], response_time: float):
    """Save assistant response to database."""
    if full_response and session_id and user_id:
        try:
            from db.session import get_db
            from db.models import ChatMessage
            from uuid import UUID
            import uuid
            
            with next(get_db()) as db:
                # Save assistant message to database
                assistant_message = ChatMessage(
                    id=uuid.uuid4(),
                    session_id=UUID(session_id),
                    user_id=user_id,
                    role="assistant",
                    content=full_response,
                    agent_id=agent_id,
                    response_time_ms=int(response_time * 1000)
                )
                db.add(assistant_message)
                db.commit()
                logger.info(f"💾 Saved assistant response to database ({len(full_response)} chars)")
                
        except Exception as db_error:
            logger.warning(f"⚠️  Could not save assistant response to database: {db_error}")
    
    # Log streaming performance
    if response_time < 5:
        logger.info(f"⚡ Stream completed in {response_time:.1f}s")
    elif response_time < 20:
        logger.info(f"✅ Stream completed in {response_time:.1f}s")
    else:
        logger.warning(f"🐌 Slow stream: {response_time:.1f}s")


@chat_router.post("/stream", status_code=status.HTTP_200_OK)
async def create_agno_stream(body: AgnoStreamRequest, structured: bool = False):
    """
    Agno streaming chat with optional ThoughtChain reasoning visualization.
    
    Args:
        structured: If True, return structured JSON events
        body.enable_thought_chain: If True, use full ThoughtChain system
    
    When structured=true and enable_thought_chain=true, provides real-time
    reasoning visualization showing agent thinking steps, tool executions,
    and decision making process.
    """
    logger.info("📥 Chat stream request received:")
    logger.info(f"   - agent_id: {body.agent_id}")
    logger.info(f"   - user_id: {body.user_id}")
    logger.info(f"   - project_id: {body.project_id}")
    logger.info(f"   - session_id: {body.session_id}")
    logger.info(f"   - message: {body.message[:100]}..." if len(body.message) > 100 else f"   - message: {body.message}")
    
    try:
        # Validate project_id for document analysis agents
        if body.agent_id in ["document_analysis_agent", "document_agent"] and not body.project_id:
            logger.warning("⚠️  Missing project_id for document analysis request")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project ID is required for document analysis. Please provide project_id in your request."
            )
        
        # Handle session_id for conversation continuity
        if not body.session_id:
            # Try to find the most recent session for this user/project to continue the conversation
            try:
                from db.session import get_db
                from sqlalchemy import text
                
                with next(get_db()) as db:
                    # Look for the most recent session for this user/project
                    query = text("""
                        SELECT session_id 
                        FROM ai.agent_sessions 
                        WHERE user_id = :user_id 
                        AND (project_id = :project_id OR (:project_id IS NULL AND project_id IS NULL))
                        ORDER BY updated_at DESC 
                        LIMIT 1
                    """)
                    
                    result = db.execute(query, {
                        "user_id": body.user_id,
                        "project_id": body.project_id
                    })
                    row = result.fetchone()
                    
                    if row:
                        body.session_id = str(row.session_id)
                        logger.info(f"🔄 Reusing existing session_id: {body.session_id}")
                    else:
                        from uuid import uuid4
                        body.session_id = str(uuid4())
                        logger.info(f"🆕 Generated new session_id: {body.session_id}")
            except Exception as e:
                # Fallback to new session if there's any error
                logger.warning(f"⚠️  Error finding existing session: {e}")
                from uuid import uuid4
                body.session_id = str(uuid4())
                logger.info(f"🆕 Generated new session_id (fallback): {body.session_id}")
        
        logger.info(f"🤖 Creating agent '{body.agent_id}' with project_id='{body.project_id}'")
        
        # Create agent with native Agno memory and storage
        agent = get_agent_by_id(
            agent_id=body.agent_id,
            model_id="gpt-4o-mini",
            user_id=body.user_id,
            session_id=body.session_id,
            debug_mode=False,
            project_id=body.project_id,
            auth_token=body.auth_token,
        )
        
        logger.info(f"🚀 Starting Agno stream for session {body.session_id}")
        
        # Save user message to database before streaming response
        try:
            from db.session import get_db
            from db.models import ChatMessage, ChatSession
            from uuid import UUID
            import uuid
            
            with next(get_db()) as db:
                # Ensure session exists in chat_sessions table
                session_uuid = UUID(body.session_id)
                existing_session = db.query(ChatSession).filter(ChatSession.id == session_uuid).first()
                
                if not existing_session:
                    # Create new chat session
                    new_session = ChatSession(
                        id=session_uuid,
                        user_id=body.user_id,
                        title="New Chat",
                        project_id=body.project_id,
                        message_count=0
                    )
                    db.add(new_session)
                    db.commit()
                    logger.info(f"🆕 Created new chat session: {body.session_id}")
                
                # Save user message to database
                user_message = ChatMessage(
                    id=uuid.uuid4(),
                    session_id=session_uuid,
                    user_id=body.user_id,
                    role="user",
                    content=body.message,
                    agent_id=body.agent_id
                )
                db.add(user_message)
                db.commit()
                logger.info(f"💾 Saved user message to database")
                
        except Exception as db_error:
            logger.warning(f"⚠️  Could not save user message to database: {db_error}")
        
        # Agno streaming with optional ThoughtChain
        return StreamingResponse(
            chat_response_streamer_with_db_save(
                agent=agent,
                message=body.message,
                session_id=body.session_id,
                agent_id=body.agent_id,
                structured=structured,
                project_id=body.project_id,
                user_id=body.user_id,
                enable_thought_chain=body.enable_thought_chain
            ),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",  # Disable nginx buffering
                "X-Session-Id": body.session_id,  # Return session_id in header
                "X-Project-Id": body.project_id or "none",  # Include project_id in header
                "X-ThoughtChain-Enabled": str(body.enable_thought_chain).lower(),
                "X-Structured": str(structured).lower(),
            }
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"❌ Error creating Agno stream: {str(e)}")
        logger.error(f"❌ Request context - agent_id: {body.agent_id}, project_id: {body.project_id}, user_id: {body.user_id}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create stream: {str(e)}"
        )


@chat_router.post("/sessions")
async def create_new_session():
    """Create a new chat session and return the session ID."""
    from uuid import uuid4
    
    new_session_id = str(uuid4())
    
    return {
        "session_id": new_session_id,
        "status": "created",
        "system": "agno_native",
        "message": "New session created. Use this session_id for subsequent requests."
    }


@chat_router.get("/sessions/{session_id}/info") 
async def get_session_info(session_id: str):
    """Get information about a specific session."""
    # Note: In pure Agno system, session info is managed by Agno internally
    return {
        "session_id": session_id,
        "status": "active",
        "system": "agno_native",
        "message": "Session managed by Agno's native storage system"
    }


@chat_router.get("/sessions/{session_id}/messages")
async def get_session_messages(
    session_id: str,
    user_id: str,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get all messages for a specific session from database."""
    try:
        from db.models import ChatMessage
        from sqlalchemy import and_
        from uuid import UUID
        
        # Convert session_id to UUID if it's a string
        try:
            session_uuid = UUID(session_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid session_id format"
            )
        
        # Query messages from database
        query = db.query(ChatMessage).filter(
            and_(
                ChatMessage.session_id == session_uuid,
                ChatMessage.user_id == user_id,
                ChatMessage.is_deleted == False
            )
        ).order_by(ChatMessage.created_at.asc())
        
        # Get total count for pagination
        total = query.count()
        
        # Apply pagination
        messages_query = query.offset(offset).limit(limit)
        db_messages = messages_query.all()
        
        # Format messages for frontend
        messages = []
        for msg in db_messages:
            messages.append({
                "id": str(msg.id),
                "role": msg.role,
                "content": msg.content,
                "agent_id": msg.agent_id,
                "model_id": msg.model_id,
                "token_count": msg.token_count,
                "response_time_ms": msg.response_time_ms,
                "timestamp": msg.created_at.isoformat() if msg.created_at else None
            })
        
        has_more = (offset + limit) < total
        
        return {
            "session_id": session_id,
            "messages": messages,
            "total": total,
            "has_more": has_more,
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error getting session messages: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get messages: {str(e)}"
        )


@chat_router.get("/sessions")
async def get_user_sessions(
    user_id: str,
    limit: int = 20,
    offset: int = 0
):
    """Get all sessions for a user (for sidebar)."""
    try:
        # Use Agno's PostgresAgentStorage to get sessions
        from agno.storage.agent.postgres import PostgresAgentStorage
        # Note: PostgresAgentStorage configured but using direct SQLAlchemy queries below
        
        # Get sessions for user - this is a simplified approach
        # Since we can't easily query the storage directly, we'll try to load recent sessions
        # by trying different session IDs or use a more direct database approach
        
        # Alternative approach: Use SQLAlchemy directly
        from db.session import get_db
        from sqlalchemy import text
        
        sessions = []
        total = 0
        
        # Use the existing SQLAlchemy session
        with next(get_db()) as db:
            # Query sessions from agent_sessions table
            query = text("""
                SELECT session_id, created_at, updated_at, user_id
                FROM ai.agent_sessions 
                WHERE user_id = :user_id 
                ORDER BY updated_at DESC 
                LIMIT :limit OFFSET :offset
            """)
            
            result = db.execute(query, {
                "user_id": user_id, 
                "limit": limit, 
                "offset": offset
            })
            rows = result.fetchall()
            
            for row in rows:
                # Get a preview message for the session
                session_id = str(row.session_id)
                
                # Try to get first message as title
                try:
                    # Get the actual agent_id from the database to create the right agent
                    agent_query = text("SELECT agent_id FROM ai.agent_sessions WHERE session_id = :session_id LIMIT 1")
                    agent_result = db.execute(agent_query, {"session_id": session_id})
                    agent_row = agent_result.fetchone()
                    actual_agent_id = agent_row.agent_id if agent_row and agent_row.agent_id else "construction_team"
                    
                    agent = get_agent_by_id(
                        agent_id=actual_agent_id, 
                        user_id=user_id,
                        session_id=session_id
                    )
                    
                    # Extract title from database memory JSON directly
                    title = "New Chat"
                    message_count = 0
                    
                    # Get memory from the current row's data
                    memory_query = text("SELECT memory FROM ai.agent_sessions WHERE session_id = :session_id LIMIT 1")
                    memory_result = db.execute(memory_query, {"session_id": session_id})
                    memory_row = memory_result.fetchone()
                    
                    if memory_row and memory_row.memory:
                        memory_data = memory_row.memory
                        
                        # Extract runs from memory
                        if 'runs' in memory_data and memory_data['runs']:
                            runs = memory_data['runs']
                            message_count = len(runs)
                            
                            # Look for the first run with user input in messages
                            for run in runs:
                                if 'messages' in run and run['messages']:
                                    for message in run['messages']:
                                        if message.get('role') == 'user' and message.get('content'):
                                            user_content = message['content'].strip()
                                            # Clean up any formatting or extra text
                                            if '\n' in user_content:
                                                user_content = user_content.split('\n')[0].strip()
                                            title = user_content[:50] + ("..." if len(user_content) > 50 else "")
                                            break
                                    if title != "New Chat":
                                        break
                    
                    # Convert Unix timestamps to ISO format with null checks
                    from datetime import datetime
                    created_at = None
                    if row.created_at:
                        created_at = datetime.fromtimestamp(row.created_at).isoformat() if isinstance(row.created_at, int) else row.created_at.isoformat()
                    
                    updated_at = None
                    if row.updated_at:
                        updated_at = datetime.fromtimestamp(row.updated_at).isoformat() if isinstance(row.updated_at, int) else row.updated_at.isoformat()
                    
                    sessions.append({
                        "session_id": session_id,
                        "title": title,
                        "message_count": message_count,
                        "created_at": created_at,
                        "updated_at": updated_at,
                        "preview": title  # Same as title for now
                    })
                    
                except Exception as session_error:
                    logger.warning(f"Could not load session {session_id}: {session_error}")
                    # Still include the session even if we can't load messages
                    from datetime import datetime
                    created_at = None
                    if row.created_at:
                        created_at = datetime.fromtimestamp(row.created_at).isoformat() if isinstance(row.created_at, int) else row.created_at.isoformat()
                    
                    updated_at = None
                    if row.updated_at:
                        updated_at = datetime.fromtimestamp(row.updated_at).isoformat() if isinstance(row.updated_at, int) else row.updated_at.isoformat()
                    
                    sessions.append({
                        "session_id": session_id,
                        "title": "Chat Session",
                        "message_count": 0,
                        "created_at": created_at,
                        "updated_at": updated_at,
                        "preview": "Unable to load preview"
                    })
            
            # Get total count
            count_query = text("SELECT COUNT(*) FROM ai.agent_sessions WHERE user_id = :user_id")
            count_result = db.execute(count_query, {"user_id": user_id})
            total = count_result.scalar() or 0
            
            return {
                "sessions": sessions,
                "total": total,
                "has_more": (offset + limit) < total,
                "limit": limit,
                "offset": offset
            }
            
    except Exception as e:
        logger.error(f"Error getting user sessions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get sessions: {str(e)}"
        )


@chat_router.delete("/sessions/{session_id}")
async def delete_session(session_id: str, user_id: str):
    """Delete a specific session."""
    try:
        from db.session import get_db
        from sqlalchemy import text
        
        with next(get_db()) as db:
            # Delete from agent_sessions table
            delete_query = text("""
                DELETE FROM ai.agent_sessions 
                WHERE session_id = :session_id AND user_id = :user_id
            """)
            result = db.execute(delete_query, {
                "session_id": session_id, 
                "user_id": user_id
            })
            
            # Also delete from memory table if exists
            try:
                memory_delete_query = text("""
                    DELETE FROM ai.user_memories 
                    WHERE session_id = :session_id AND user_id = :user_id
                """)
                db.execute(memory_delete_query, {
                    "session_id": session_id,
                    "user_id": user_id
                })
            except Exception as memory_error:
                logger.warning(f"Could not delete memory for session {session_id}: {memory_error}")
            
            # Commit the transaction
            db.commit()
            
            if result.rowcount == 0:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Session not found or access denied"
                )
            
            return {
                "session_id": session_id,
                "status": "deleted",
                "message": "Session deleted successfully"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete session: {str(e)}"
        )


@chat_router.post("/complete")
async def create_agno_complete(body: AgnoStreamRequest):
    """
    Non-streaming chat - optimized for performance.
    """
    logger.info("📥 Chat complete request received:")
    logger.info(f"   - agent_id: {body.agent_id}")
    logger.info(f"   - user_id: {body.user_id}")
    
    start_time = time.time()
    
    try:
        # Validate project_id for document analysis agents
        if body.agent_id in ["document_analysis_agent", "document_agent"] and not body.project_id:
            logger.warning("⚠️  Missing project_id for document analysis request")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project ID is required for document analysis. Please provide project_id in your request."
            )
        
        # Handle session_id (same logic as streaming endpoint)
        if not body.session_id:
            try:
                from db.session import get_db
                from sqlalchemy import text
                
                with next(get_db()) as db:
                    query = text("""
                        SELECT session_id 
                        FROM ai.agent_sessions 
                        WHERE user_id = :user_id 
                        AND (project_id = :project_id OR (:project_id IS NULL AND project_id IS NULL))
                        ORDER BY updated_at DESC 
                        LIMIT 1
                    """)
                    
                    result = db.execute(query, {
                        "user_id": body.user_id,
                        "project_id": body.project_id
                    })
                    row = result.fetchone()
                    
                    if row:
                        body.session_id = str(row.session_id)
                        logger.info(f"🔄 Reusing existing session_id: {body.session_id}")
                    else:
                        from uuid import uuid4
                        body.session_id = str(uuid4())
                        logger.info(f"🆕 Generated new session_id: {body.session_id}")
            except Exception as e:
                logger.warning(f"⚠️  Error finding existing session: {e}")
                from uuid import uuid4
                body.session_id = str(uuid4())
                logger.info(f"🆕 Generated new session_id (fallback): {body.session_id}")
        
        # Create agent
        agent = get_agent_by_id(
            agent_id=body.agent_id,
            model_id="gpt-4o-mini",
            user_id=body.user_id,
            session_id=body.session_id,
            debug_mode=False,
            project_id=body.project_id,
            auth_token=body.auth_token,
        )
        
        # Run agent directly without optimizer
        response = await agent.arun(body.message, stream=False)
        agent_response = response.content
        response_time = (time.time() - start_time) * 1000
        
        # Layer 3 Safety Net: Filter content_text from complete response
        filtered_response = filter_content_text_from_response(agent_response)
        
        # Return simplified response
        return {
            "message": filtered_response,
            "session_id": body.session_id,
            "agent_id": body.agent_id,
            "response_time_ms": response_time,
            "is_streaming": False,
            "stream_complete": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error in complete chat: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process request: {str(e)}"
        )


@chat_router.get("/health")
async def chat_health():
    """Simple health check for chat service."""
    return {"status": "healthy", "system": "agno_native"}
